import React, { useRef, useEffect } from 'react'
import { useVideoPlayer } from '@/hooks/useVideoPlayer'

export default function OptimizedVideoPlayer({ data, setShowVideoPlayer }) {
  const containerRef = useRef(null)
  const controlsTimeoutRef = useRef(null)
  
  const {
    videoRef,
    isPlaying,
    currentTime,
    duration,
    volume,
    isMuted,
    isLoading,
    hasError,
    isBuffering,
    canPlay,
    isMobile,
    togglePlayPause,
    seekTo,
    skipTime,
    setVideoVolume,
    toggleMute,
    formatTime,
    videoEventHandlers,
  } = useVideoPlayer(data?.url)

  const [showControls, setShowControls] = React.useState(true)
  const [isFullscreen, setIsFullscreen] = React.useState(false)

  // Auto-hide controls
  useEffect(() => {
    const resetControlsTimeout = () => {
      if (controlsTimeoutRef.current) {
        clearTimeout(controlsTimeoutRef.current)
      }

      setShowControls(true)

      if (isPlaying) {
        controlsTimeoutRef.current = setTimeout(() => {
          setShowControls(false)
        }, 3000)
      }
    }

    resetControlsTimeout()

    return () => {
      if (controlsTimeoutRef.current) {
        clearTimeout(controlsTimeoutRef.current)
      }
    }
  }, [isPlaying])

  // Handle fullscreen
  const toggleFullscreen = React.useCallback(() => {
    if (!containerRef.current) return

    try {
      if (!document.fullscreenElement) {
        const element = containerRef.current
        if (element.requestFullscreen) {
          element.requestFullscreen()
        } else if (element.webkitRequestFullscreen) {
          element.webkitRequestFullscreen()
        } else if (element.mozRequestFullScreen) {
          element.mozRequestFullScreen()
        } else if (element.msRequestFullscreen) {
          element.msRequestFullscreen()
        } else if (videoRef.current?.webkitEnterFullscreen) {
          // iOS Safari fallback
          videoRef.current.webkitEnterFullscreen()
        }
        setIsFullscreen(true)
      } else {
        if (document.exitFullscreen) {
          document.exitFullscreen()
        } else if (document.webkitExitFullscreen) {
          document.webkitExitFullscreen()
        } else if (document.mozCancelFullScreen) {
          document.mozCancelFullScreen()
        } else if (document.msExitFullscreen) {
          document.msExitFullscreen()
        }
        setIsFullscreen(false)
      }
    } catch (error) {
      console.warn('Fullscreen toggle failed:', error)
    }
  }, [])

  // Handle progress bar click
  const handleProgressClick = React.useCallback((e) => {
    if (!videoRef.current || !e.currentTarget) return

    const rect = e.currentTarget.getBoundingClientRect()
    const clickX = e.clientX - rect.left
    const newTime = (clickX / rect.width) * duration
    seekTo(newTime)
  }, [duration, seekTo])

  // Close video player
  const handleClose = React.useCallback(() => {
    if (videoRef.current) {
      videoRef.current.pause()
      videoRef.current.currentTime = 0
    }
    setShowVideoPlayer(false)
  }, [setShowVideoPlayer])

  // Handle fullscreen change events
  useEffect(() => {
    const handleFullscreenChange = () => {
      setIsFullscreen(!!document.fullscreenElement)
    }

    document.addEventListener('fullscreenchange', handleFullscreenChange)
    document.addEventListener('webkitfullscreenchange', handleFullscreenChange)
    document.addEventListener('mozfullscreenchange', handleFullscreenChange)
    document.addEventListener('MSFullscreenChange', handleFullscreenChange)

    return () => {
      document.removeEventListener('fullscreenchange', handleFullscreenChange)
      document.removeEventListener('webkitfullscreenchange', handleFullscreenChange)
      document.removeEventListener('mozfullscreenchange', handleFullscreenChange)
      document.removeEventListener('MSFullscreenChange', handleFullscreenChange)
    }
  }, [])

  return (
    <>
      {/* Custom Styles for iPad/iOS compatibility */}
      <style jsx>{`
        .video-container {
          -webkit-overflow-scrolling: touch;
          touch-action: manipulation;
        }
        
        .video-container:fullscreen {
          background: black;
          display: flex;
          align-items: center;
          justify-content: center;
        }

        .video-container:fullscreen video {
          width: 100vw;
          height: 100vh;
          object-fit: contain;
        }

        .touch-button {
          -webkit-tap-highlight-color: transparent;
          touch-action: manipulation;
          user-select: none;
          -webkit-user-select: none;
        }

        .progress-bar {
          touch-action: manipulation;
          cursor: pointer;
        }

        .volume-slider {
          -webkit-appearance: none;
          appearance: none;
          background: rgba(255, 255, 255, 0.3);
          outline: none;
          border-radius: 5px;
          touch-action: manipulation;
        }

        .volume-slider::-webkit-slider-thumb {
          -webkit-appearance: none;
          appearance: none;
          width: 15px;
          height: 15px;
          border-radius: 50%;
          background: #3b82f6;
          cursor: pointer;
          border: 2px solid white;
        }

        .volume-slider::-moz-range-thumb {
          width: 15px;
          height: 15px;
          border-radius: 50%;
          background: #3b82f6;
          cursor: pointer;
          border: 2px solid white;
        }
      `}</style>

      <div className='popupWrapper flex z-10 fixed top-0 left-0 w-full h-full bg-black/50'>
        {/* Close Button */}
        <div
          onClick={handleClose}
          className="flex z-10 items-center justify-center absolute top-0 right-[104px] h-[75px] w-[96px] cursor-pointer touch-button"
        >
          <div className={`rotate-45 bg-[#e7e0da] h-1 m-auto absolute w-10 duration-300 ease-linear`}/>
          <div className={`-rotate-45 bg-[#e7e0da] h-1 m-auto absolute w-10 duration-300 ease-linear`}/>
        </div>

        <div className='flex relative top-[75px] left-[75px] w-[calc(100%-75px)] h-[calc(100%-75px)] items-center justify-center overflow-y-auto overflow-x-hidden'>
          <div className="relative w-full max-w-4xl bg-black rounded-xl shadow-2xl overflow-hidden">
            {/* Video Container */}
            <div
              ref={containerRef}
              className="relative w-full aspect-video bg-black group cursor-pointer video-container"
              onClick={togglePlayPause}
              onMouseMove={() => setShowControls(true)}
              onTouchStart={() => setShowControls(true)}
            >
              {/* Main Video Element */}
              <video
                ref={videoRef}
                src={data?.url}
                poster={data?.thumbnail}
                className="w-full h-full object-contain"
                preload="metadata"
                playsInline
                webkit-playsinline="true"
                controls={false}
                crossOrigin="anonymous"
                {...videoEventHandlers}
              />

              {/* Loading Spinner */}
              {(isLoading || isBuffering) && (
                <div className="absolute inset-0 flex items-center justify-center bg-black bg-opacity-50">
                  <div className="animate-spin rounded-full h-16 w-16 border-4 border-white border-t-transparent"></div>
                </div>
              )}

              {/* Error State */}
              {hasError && (
                <div className="absolute inset-0 flex items-center justify-center bg-black bg-opacity-75">
                  <div className="text-center text-white">
                    <svg className="mx-auto h-16 w-16 mb-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                    <h3 className="text-lg font-medium mb-2">Video Error</h3>
                    <p className="text-sm text-gray-300">Unable to load video. Please try again.</p>
                  </div>
                </div>
              )}

              {/* Center Play Button */}
              {!isPlaying && !isLoading && !hasError && canPlay && (
                <div className="absolute inset-0 flex items-center justify-center">
                  <button
                    onClick={(e) => {
                      e.stopPropagation()
                      togglePlayPause()
                    }}
                    className="bg-white bg-opacity-20 hover:bg-opacity-30 active:bg-opacity-40 rounded-full p-6 transition-all duration-300 transform hover:scale-110 active:scale-95 touch-button"
                    aria-label="Play video"
                  >
                    <svg className="h-12 w-12 text-white ml-1" fill="currentColor" viewBox="0 0 24 24">
                      <path d="M8 5v14l11-7z"/>
                    </svg>
                  </button>
                </div>
              )}

              {/* Mobile Touch Overlay */}
              {isMobile && (
                <div 
                  className="absolute inset-0 z-10"
                  onTouchEnd={(e) => {
                    e.preventDefault()
                    e.stopPropagation()
                    togglePlayPause()
                  }}
                  style={{ WebkitTapHighlightColor: 'transparent' }}
                />
              )}

              {/* Video Controls */}
              <div className={`absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black via-black/50 to-transparent p-4 transition-opacity duration-300 ${showControls ? 'opacity-100' : 'opacity-0'}`}>
                {/* Progress Bar */}
                <div className="mb-4">
                  <div
                    className="w-full h-2 bg-white bg-opacity-30 rounded-full cursor-pointer hover:h-3 transition-all duration-200 progress-bar"
                    onClick={handleProgressClick}
                  >
                    <div
                      className="h-full bg-blue-500 rounded-full transition-all duration-200"
                      style={{ width: `${duration ? (currentTime / duration) * 100 : 0}%` }}
                    />
                  </div>
                </div>

                {/* Control Buttons */}
                <div className="flex items-center justify-between">
                  {/* Left Controls */}
                  <div className="flex items-center space-x-4">
                    {/* Play/Pause */}
                    <button
                      onClick={togglePlayPause}
                      className="text-white hover:text-blue-400 transition-colors p-2 touch-button"
                      aria-label={isPlaying ? 'Pause' : 'Play'}
                    >
                      {isPlaying ? (
                        <svg className="h-6 w-6" fill="currentColor" viewBox="0 0 24 24">
                          <path d="M6 4h4v16H6V4zm8 0h4v16h-4V4z"/>
                        </svg>
                      ) : (
                        <svg className="h-6 w-6" fill="currentColor" viewBox="0 0 24 24">
                          <path d="M8 5v14l11-7z"/>
                        </svg>
                      )}
                    </button>

                    {/* Skip Controls */}
                    <button
                      onClick={() => skipTime(-10)}
                      className="text-white hover:text-blue-400 transition-colors p-2 touch-button"
                      aria-label="Skip backward 10 seconds"
                    >
                      <svg className="h-5 w-5" fill="currentColor" viewBox="0 0 24 24">
                        <path d="M11.99 5V1l-5 5 5 5V7c3.31 0 6 2.69 6 6s-2.69 6-6 6-6-2.69-6-6h-2c0 4.42 3.58 8 8 8s8-3.58 8-8-3.58-8-8-8z"/>
                      </svg>
                    </button>

                    <button
                      onClick={() => skipTime(10)}
                      className="text-white hover:text-blue-400 transition-colors p-2 touch-button"
                      aria-label="Skip forward 10 seconds"
                    >
                      <svg className="h-5 w-5" fill="currentColor" viewBox="0 0 24 24">
                        <path d="M12 5V1l5 5-5 5V7c-3.31 0-6 2.69-6 6s2.69 6 6 6 6-2.69 6-6h2c0 4.42-3.58 8-8 8s-8-3.58-8-8 3.58-8 8-8z"/>
                      </svg>
                    </button>

                    {/* Volume Controls - Hidden on mobile */}
                    {!isMobile && (
                      <div className="flex items-center space-x-2">
                        <button
                          onClick={toggleMute}
                          className="text-white hover:text-blue-400 transition-colors p-2 touch-button"
                          aria-label={isMuted ? 'Unmute' : 'Mute'}
                        >
                          {isMuted || volume === 0 ? (
                            <svg className="h-5 w-5" fill="currentColor" viewBox="0 0 24 24">
                              <path d="M16.5 12c0-1.77-1.02-3.29-2.5-4.03v2.21l2.45 2.45c.03-.2.05-.41.05-.63zm2.5 0c0 .94-.2 1.82-.54 2.64l1.51 1.51C20.63 14.91 21 13.5 21 12c0-4.28-2.99-7.86-7-8.77v2.06c2.89.86 5 3.54 5 6.71zM4.27 3L3 4.27 7.73 9H3v6h4l5 5v-6.73l4.25 4.25c-.67.52-1.42.93-2.25 1.18v2.06c1.38-.31 2.63-.95 3.69-1.81L19.73 21 21 19.73l-9-9L4.27 3zM12 4L9.91 6.09 12 8.18V4z"/>
                            </svg>
                          ) : (
                            <svg className="h-5 w-5" fill="currentColor" viewBox="0 0 24 24">
                              <path d="M3 9v6h4l5 5V4L7 9H3zm13.5 3c0-1.77-1.02-3.29-2.5-4.03v8.05c1.48-.73 2.5-2.25 2.5-4.02zM14 3.23v2.06c2.89.86 5 3.54 5 6.71s-2.11 5.85-5 6.71v2.06c4.01-.91 7-4.49 7-8.77s-2.99-7.86-7-8.77z"/>
                            </svg>
                          )}
                        </button>

                        <input
                          type="range"
                          min="0"
                          max="1"
                          step="0.1"
                          value={isMuted ? 0 : volume}
                          onChange={(e) => setVideoVolume(parseFloat(e.target.value))}
                          className="w-20 h-1 bg-white bg-opacity-30 rounded-lg appearance-none cursor-pointer volume-slider"
                          aria-label="Volume"
                        />
                      </div>
                    )}

                    {/* Time Display */}
                    <div className="text-white text-sm font-mono">
                      {formatTime(currentTime)} / {formatTime(duration)}
                    </div>
                  </div>

                  {/* Right Controls */}
                  <div className="flex items-center space-x-2">
                    {/* Fullscreen Toggle */}
                    <button
                      onClick={toggleFullscreen}
                      className="text-white hover:text-blue-400 transition-colors p-2 touch-button"
                      aria-label={isFullscreen ? 'Exit fullscreen' : 'Enter fullscreen'}
                    >
                      {isFullscreen ? (
                        <svg className="h-5 w-5" fill="currentColor" viewBox="0 0 24 24">
                          <path d="M5 16h3v3h2v-5H5v2zm3-8H5v2h5V5H8v3zm6 11h2v-3h3v-2h-5v5zm2-11V5h-2v5h5V8h-3z"/>
                        </svg>
                      ) : (
                        <svg className="h-5 w-5" fill="currentColor" viewBox="0 0 24 24">
                          <path d="M7 14H5v5h5v-2H7v-3zm-2-4h2V7h3V5H5v5zm12 7h-3v2h5v-5h-2v3zM14 5v2h3v3h2V5h-5z"/>
                        </svg>
                      )}
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </>
  )
}
