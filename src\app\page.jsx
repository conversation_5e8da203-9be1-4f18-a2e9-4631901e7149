import React, { Suspense } from 'react'
import ImageScalerComponent from '@/components/ImageScalerComponent';
import Navbar from '@/components/Navbar';
import ImageWrapperResponsive from '@/components/ImageWrapperResponsive';
import Image from 'next/image';

export default function page() {
  return (
    <main 
      style={{width:'auto',height:'100%',objectFit:'cover'}} 
      className="flex relative items-center justify-center overflow-hidden"
    >
      <img 
        style={{width:'auto',height:'100%',objectFit:'cover'}} 
        src="/assets/website_under_construction_002.jpg" alt="" 
      />
      {/* <Image 
        // style={{width:'auto',height:'100%',objectFit:'cover'}} 
        src={'/assets/website_under_construction_002.jpg'} 
        alt='landingpage backround image'
        fill
      /> */}
      <Suspense fallback={null}>
      </Suspense>
    </main>
  )
}
