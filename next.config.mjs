/** @type {import('next').NextConfig} */
const nextConfig = {
    images: {
        domains: ['firebasestorage.googleapis.com'],
        unoptimized: true,
    },
    async headers() {
        return [
            {
                // Apply headers to all video files
                source: '/(.*\\.(mp4|webm|ogg|avi|mov))',
                headers: [
                    {
                        key: 'Cache-Control',
                        value: 'public, max-age=31536000, immutable',
                    },
                    {
                        key: 'Accept-Ranges',
                        value: 'bytes',
                    },
                    {
                        key: 'Content-Type',
                        value: 'video/mp4',
                    },
                ],
            },
            {
                // Apply CORS headers for Firebase Storage
                source: '/api/(.*)',
                headers: [
                    {
                        key: 'Access-Control-Allow-Origin',
                        value: '*',
                    },
                    {
                        key: 'Access-Control-Allow-Methods',
                        value: 'GET, POST, PUT, DELETE, OPTIONS',
                    },
                    {
                        key: 'Access-Control-Allow-Headers',
                        value: 'Content-Type, Authorization',
                    },
                ],
            },
        ];
    },
    experimental: {
        // Enable streaming for better video performance
        serverComponentsExternalPackages: [],
    },
};

export default nextConfig;
