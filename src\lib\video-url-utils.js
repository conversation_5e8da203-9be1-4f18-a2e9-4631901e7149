/**
 * Utility functions for handling video URLs and ensuring proper serving
 */

/**
 * Convert local upload paths to proper API serving URLs
 * @param {string} url - The original URL (could be local path or Firebase URL)
 * @returns {string} - Properly formatted URL for video serving
 */
export function normalizeVideoUrl(url) {
  if (!url) return '';
  
  // If it's already a Firebase URL, return as-is
  if (url.includes('firebasestorage.googleapis.com')) {
    return url;
  }
  
  // If it's already an API URL, return as-is
  if (url.startsWith('/api/uploads/')) {
    return url;
  }
  
  // If it's a local uploads path, convert to API URL
  if (url.startsWith('/uploads/')) {
    return `/api${url}`;
  }
  
  // If it's a relative path without leading slash, add it
  if (url.startsWith('uploads/')) {
    return `/api/${url}`;
  }
  
  // If it's an absolute HTTP/HTTPS URL, return as-is
  if (url.startsWith('http://') || url.startsWith('https://')) {
    return url;
  }
  
  // Default: assume it's a local path and convert to API URL
  return `/api/uploads/${url}`;
}

/**
 * Check if a URL is a local upload that needs API serving
 * @param {string} url - The URL to check
 * @returns {boolean} - True if it's a local upload
 */
export function isLocalUpload(url) {
  if (!url) return false;
  
  return url.includes('/uploads/') && !url.includes('firebasestorage.googleapis.com');
}

/**
 * Get the file extension from a URL
 * @param {string} url - The URL
 * @returns {string} - File extension (e.g., 'mp4', 'webm')
 */
export function getFileExtension(url) {
  if (!url) return '';
  
  try {
    const pathname = new URL(url, 'http://localhost').pathname;
    const extension = pathname.split('.').pop()?.toLowerCase();
    return extension || '';
  } catch {
    // Fallback for relative URLs
    const extension = url.split('.').pop()?.toLowerCase();
    return extension || '';
  }
}

/**
 * Check if a URL points to a video file
 * @param {string} url - The URL to check
 * @returns {boolean} - True if it's a video file
 */
export function isVideoFile(url) {
  if (!url) return false;
  
  const videoExtensions = ['mp4', 'webm', 'avi', 'mov', 'mkv', 'flv', 'wmv', 'm4v'];
  const extension = getFileExtension(url);
  
  return videoExtensions.includes(extension);
}

/**
 * Get MIME type for a video file
 * @param {string} url - The video URL
 * @returns {string} - MIME type
 */
export function getVideoMimeType(url) {
  if (!url) return 'video/mp4'; // Default
  
  const extension = getFileExtension(url);
  
  const mimeTypes = {
    'mp4': 'video/mp4',
    'webm': 'video/webm',
    'avi': 'video/x-msvideo',
    'mov': 'video/quicktime',
    'mkv': 'video/x-matroska',
    'flv': 'video/x-flv',
    'wmv': 'video/x-ms-wmv',
    'm4v': 'video/mp4',
  };
  
  return mimeTypes[extension] || 'video/mp4';
}

/**
 * Create a video element to test if URL is accessible
 * @param {string} url - The video URL to test
 * @returns {Promise<boolean>} - True if video can be loaded
 */
export function testVideoUrl(url) {
  return new Promise((resolve) => {
    if (!url) {
      resolve(false);
      return;
    }
    
    const video = document.createElement('video');
    video.preload = 'metadata';
    
    const timeout = setTimeout(() => {
      resolve(false);
    }, 5000); // 5 second timeout
    
    video.onloadedmetadata = () => {
      clearTimeout(timeout);
      resolve(true);
    };
    
    video.onerror = () => {
      clearTimeout(timeout);
      resolve(false);
    };
    
    video.src = normalizeVideoUrl(url);
  });
}

/**
 * Generate a thumbnail URL for a video (if supported)
 * @param {string} videoUrl - The video URL
 * @returns {string|null} - Thumbnail URL or null
 */
export function getVideoThumbnail(videoUrl) {
  if (!videoUrl) return null;
  
  // For local uploads, we could generate thumbnails server-side
  // For now, return null and let the video element handle it
  return null;
}

/**
 * Format video duration from seconds to readable format
 * @param {number} seconds - Duration in seconds
 * @returns {string} - Formatted duration (e.g., "1:23" or "1:23:45")
 */
export function formatVideoDuration(seconds) {
  if (!seconds || isNaN(seconds)) return '0:00';
  
  const hours = Math.floor(seconds / 3600);
  const minutes = Math.floor((seconds % 3600) / 60);
  const secs = Math.floor(seconds % 60);
  
  if (hours > 0) {
    return `${hours}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
  } else {
    return `${minutes}:${secs.toString().padStart(2, '0')}`;
  }
}

/**
 * Check if the current device/browser supports a video format
 * @param {string} mimeType - The MIME type to check
 * @returns {boolean} - True if supported
 */
export function isVideoFormatSupported(mimeType) {
  if (!mimeType) return false;
  
  const video = document.createElement('video');
  return video.canPlayType(mimeType) !== '';
}

/**
 * Get the best video format for the current device
 * @param {Array<string>} urls - Array of video URLs with different formats
 * @returns {string|null} - Best URL for the device
 */
export function getBestVideoFormat(urls) {
  if (!urls || !Array.isArray(urls) || urls.length === 0) return null;
  
  // Priority order for video formats (best to worst)
  const formatPriority = ['mp4', 'webm', 'mov', 'avi'];
  
  for (const format of formatPriority) {
    const url = urls.find(u => getFileExtension(u) === format);
    if (url && isVideoFormatSupported(getVideoMimeType(url))) {
      return url;
    }
  }
  
  // Fallback to first URL
  return urls[0];
}
