import { NextResponse } from 'next/server';
import { promises as fs } from 'fs';
import path from 'path';

/**
 * Serve uploaded files (videos, images) with proper headers for iPad/iOS compatibility
 * This API route handles serving files from the uploads directory
 */

// GET /api/uploads/[...path] - Serve uploaded files with proper headers
export async function GET(request, { params }) {
  try {
    const { path: filePath } = await params;
    
    if (!filePath || !Array.isArray(filePath)) {
      return NextResponse.json(
        { error: 'Invalid file path' },
        { status: 400 }
      );
    }

    // Reconstruct the file path
    const requestedPath = filePath.join('/');
    
    // Security: Prevent directory traversal
    if (requestedPath.includes('..') || requestedPath.includes('\\')) {
      return NextResponse.json(
        { error: 'Invalid file path' },
        { status: 400 }
      );
    }

    // Construct the full file path
    const fullPath = path.join(process.cwd(), 'public', 'uploads', requestedPath);
    
    console.log('Serving uploaded file:', fullPath);

    // Check if file exists
    try {
      await fs.access(fullPath);
    } catch (error) {
      console.error('File not found:', fullPath);
      return NextResponse.json(
        { error: 'File not found' },
        { status: 404 }
      );
    }

    // Read the file
    const fileBuffer = await fs.readFile(fullPath);
    const stats = await fs.stat(fullPath);

    // Determine MIME type based on file extension
    const ext = path.extname(requestedPath).toLowerCase();
    let mimeType = 'application/octet-stream'; // Default

    const mimeTypes = {
      // Video formats
      '.mp4': 'video/mp4',
      '.webm': 'video/webm',
      '.avi': 'video/x-msvideo',
      '.mov': 'video/quicktime',
      '.mkv': 'video/x-matroska',
      '.flv': 'video/x-flv',
      '.wmv': 'video/x-ms-wmv',
      
      // Image formats
      '.jpg': 'image/jpeg',
      '.jpeg': 'image/jpeg',
      '.png': 'image/png',
      '.gif': 'image/gif',
      '.webp': 'image/webp',
      '.svg': 'image/svg+xml',
      '.bmp': 'image/bmp',
      '.ico': 'image/x-icon',
      
      // Audio formats
      '.mp3': 'audio/mpeg',
      '.wav': 'audio/wav',
      '.ogg': 'audio/ogg',
      '.aac': 'audio/aac',
      '.flac': 'audio/flac',
      
      // Document formats
      '.pdf': 'application/pdf',
      '.doc': 'application/msword',
      '.docx': 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
      '.txt': 'text/plain',
      '.json': 'application/json',
      '.xml': 'application/xml',
    };

    if (mimeTypes[ext]) {
      mimeType = mimeTypes[ext];
    }

    // Handle range requests for video streaming (important for iPad/iOS)
    const range = request.headers.get('range');
    
    if (range && mimeType.startsWith('video/')) {
      const parts = range.replace(/bytes=/, "").split("-");
      const start = parseInt(parts[0], 10);
      const end = parts[1] ? parseInt(parts[1], 10) : stats.size - 1;
      const chunksize = (end - start) + 1;
      
      const fileStream = fileBuffer.slice(start, end + 1);
      
      return new NextResponse(fileStream, {
        status: 206, // Partial Content
        headers: {
          'Content-Range': `bytes ${start}-${end}/${stats.size}`,
          'Accept-Ranges': 'bytes',
          'Content-Length': chunksize.toString(),
          'Content-Type': mimeType,
          'Cache-Control': 'public, max-age=31536000, immutable',
          'Access-Control-Allow-Origin': '*',
          'Access-Control-Allow-Methods': 'GET, HEAD, OPTIONS',
          'Access-Control-Allow-Headers': 'Range, Content-Type, Authorization',
          'Cross-Origin-Resource-Policy': 'cross-origin',
          'X-Content-Type-Options': 'nosniff',
        },
      });
    }

    // Regular file serving (non-range request)
    const response = new NextResponse(fileBuffer, {
      status: 200,
      headers: {
        'Content-Type': mimeType,
        'Content-Length': fileBuffer.length.toString(),
        'Cache-Control': 'public, max-age=31536000, immutable',
        'Accept-Ranges': 'bytes',
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Methods': 'GET, HEAD, OPTIONS',
        'Access-Control-Allow-Headers': 'Range, Content-Type, Authorization',
        'Cross-Origin-Resource-Policy': 'cross-origin',
        'X-Content-Type-Options': 'nosniff',
        'Last-Modified': stats.mtime.toUTCString(),
        'ETag': `"${stats.size}-${stats.mtime.getTime()}"`,
      },
    });

    return response;

  } catch (error) {
    console.error('Error serving uploaded file:', error);
    return NextResponse.json(
      { error: 'Internal server error', details: error.message },
      { status: 500 }
    );
  }
}

// HEAD /api/uploads/[...path] - Handle HEAD requests for video preloading
export async function HEAD(request, { params }) {
  try {
    const { path: filePath } = await params;
    
    if (!filePath || !Array.isArray(filePath)) {
      return new NextResponse(null, { status: 400 });
    }

    const requestedPath = filePath.join('/');
    
    if (requestedPath.includes('..') || requestedPath.includes('\\')) {
      return new NextResponse(null, { status: 400 });
    }

    const fullPath = path.join(process.cwd(), 'public', 'uploads', requestedPath);

    try {
      const stats = await fs.stat(fullPath);
      const ext = path.extname(requestedPath).toLowerCase();
      
      let mimeType = 'application/octet-stream';
      if (ext === '.mp4') mimeType = 'video/mp4';
      else if (ext === '.webm') mimeType = 'video/webm';
      else if (ext === '.avi') mimeType = 'video/x-msvideo';
      else if (ext === '.mov') mimeType = 'video/quicktime';

      return new NextResponse(null, {
        status: 200,
        headers: {
          'Content-Type': mimeType,
          'Content-Length': stats.size.toString(),
          'Accept-Ranges': 'bytes',
          'Cache-Control': 'public, max-age=31536000, immutable',
          'Access-Control-Allow-Origin': '*',
          'Last-Modified': stats.mtime.toUTCString(),
          'ETag': `"${stats.size}-${stats.mtime.getTime()}"`,
        },
      });
    } catch (error) {
      return new NextResponse(null, { status: 404 });
    }
  } catch (error) {
    return new NextResponse(null, { status: 500 });
  }
}

// OPTIONS /api/uploads/[...path] - Handle CORS preflight requests
export async function OPTIONS() {
  return new NextResponse(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, HEAD, OPTIONS',
      'Access-Control-Allow-Headers': 'Range, Content-Type, Authorization',
      'Access-Control-Max-Age': '86400',
    },
  });
}
