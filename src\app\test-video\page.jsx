'use client';
import { useState, useEffect } from 'react';
import { normalizeVideoUrl } from '@/lib/video-url-utils';

export default function TestVideoPage() {
  const [videos, setVideos] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    fetchVideos();
  }, []);

  const fetchVideos = async () => {
    try {
      const response = await fetch('/api/video-gallery');
      const data = await response.json();
      
      if (data.success) {
        setVideos(data.data);
      } else {
        setError('Failed to fetch videos');
      }
    } catch (err) {
      setError('Error fetching videos: ' + err.message);
    } finally {
      setLoading(false);
    }
  };

  const testVideoUrl = (url) => {
    const normalizedUrl = normalizeVideoUrl(url);
    console.log('Original URL:', url);
    console.log('Normalized URL:', normalizedUrl);
    return normalizedUrl;
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-100 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">Loading videos...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen bg-gray-100 flex items-center justify-center">
        <div className="text-center">
          <p className="text-red-600 text-xl">{error}</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-100 py-8">
      <div className="max-w-6xl mx-auto px-4">
        <h1 className="text-3xl font-bold text-gray-900 mb-8">Video URL Test Page</h1>
        
        <div className="bg-white rounded-lg shadow-md p-6 mb-8">
          <h2 className="text-xl font-semibold mb-4">URL Normalization Test</h2>
          <div className="space-y-4">
            <div>
              <p className="font-medium">Test URLs:</p>
              <div className="bg-gray-50 p-4 rounded mt-2">
                <p><strong>Original:</strong> /uploads/video-gallery/1749325680461.webm</p>
                <p><strong>Normalized:</strong> {testVideoUrl('/uploads/video-gallery/1749325680461.webm')}</p>
              </div>
            </div>
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {videos.map((video) => {
            const originalUrl = video.url;
            const normalizedUrl = normalizeVideoUrl(video.url);
            
            return (
              <div key={video._id} className="bg-white rounded-lg shadow-md overflow-hidden">
                <div className="p-4">
                  <h3 className="text-lg font-semibold mb-2">{video.title}</h3>
                  
                  <div className="mb-4">
                    <p className="text-sm text-gray-600 mb-1"><strong>Original URL:</strong></p>
                    <p className="text-xs bg-gray-100 p-2 rounded break-all">{originalUrl}</p>
                  </div>
                  
                  <div className="mb-4">
                    <p className="text-sm text-gray-600 mb-1"><strong>Normalized URL:</strong></p>
                    <p className="text-xs bg-blue-100 p-2 rounded break-all">{normalizedUrl}</p>
                  </div>

                  {/* Video Test */}
                  <div className="mb-4">
                    <p className="text-sm text-gray-600 mb-2"><strong>Video Test:</strong></p>
                    <video
                      src={normalizedUrl}
                      className="w-full h-32 object-cover rounded border"
                      controls
                      preload="metadata"
                      onError={(e) => {
                        console.error('Video error for', normalizedUrl, e);
                        e.target.style.border = '2px solid red';
                      }}
                      onLoadedMetadata={(e) => {
                        console.log('Video loaded successfully:', normalizedUrl);
                        e.target.style.border = '2px solid green';
                      }}
                    />
                  </div>

                  {/* Direct Link Test */}
                  <div className="space-y-2">
                    <a
                      href={normalizedUrl}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="block w-full text-center bg-blue-600 text-white py-2 px-4 rounded hover:bg-blue-700 transition-colors"
                    >
                      Test Direct Link
                    </a>
                    
                    <button
                      onClick={() => {
                        fetch(normalizedUrl, { method: 'HEAD' })
                          .then(response => {
                            console.log('HEAD request response:', response.status, response.headers);
                            alert(`HEAD request: ${response.status} ${response.statusText}`);
                          })
                          .catch(error => {
                            console.error('HEAD request error:', error);
                            alert(`HEAD request failed: ${error.message}`);
                          });
                      }}
                      className="block w-full text-center bg-green-600 text-white py-2 px-4 rounded hover:bg-green-700 transition-colors"
                    >
                      Test HEAD Request
                    </button>
                  </div>
                </div>
              </div>
            );
          })}
        </div>

        {videos.length === 0 && (
          <div className="text-center py-12">
            <p className="text-gray-600 text-xl">No videos found</p>
          </div>
        )}
      </div>
    </div>
  );
}
