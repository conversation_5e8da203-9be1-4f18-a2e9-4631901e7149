'use client';
import { normalizeVideoUrl } from '@/lib/video-url-utils';

export default function TestVideoDirectPage() {
  // Test with known video files from the uploads directory
  const testVideos = [
    '/uploads/video-gallery/1749325680461.webm',
    '/uploads/video-gallery/1749325799712.webm'
  ];

  const testVideoUrl = (url) => {
    const normalizedUrl = normalizeVideoUrl(url);
    console.log('Original URL:', url);
    console.log('Normalized URL:', normalizedUrl);
    return normalizedUrl;
  };

  return (
    <div className="min-h-screen bg-gray-100 py-8">
      <div className="max-w-4xl mx-auto px-4">
        <h1 className="text-3xl font-bold text-gray-900 mb-8">Direct Video API Test</h1>
        
        <div className="bg-white rounded-lg shadow-md p-6 mb-8">
          <h2 className="text-xl font-semibold mb-4">URL Normalization Test</h2>
          <div className="space-y-4">
            {testVideos.map((url, index) => {
              const normalizedUrl = testVideoUrl(url);
              return (
                <div key={index} className="border-b pb-4">
                  <p className="font-medium">Test Video {index + 1}:</p>
                  <div className="bg-gray-50 p-4 rounded mt-2">
                    <p><strong>Original:</strong> {url}</p>
                    <p><strong>Normalized:</strong> {normalizedUrl}</p>
                  </div>
                </div>
              );
            })}
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {testVideos.map((originalUrl, index) => {
            const normalizedUrl = normalizeVideoUrl(originalUrl);
            
            return (
              <div key={index} className="bg-white rounded-lg shadow-md overflow-hidden">
                <div className="p-4">
                  <h3 className="text-lg font-semibold mb-2">Test Video {index + 1}</h3>
                  
                  <div className="mb-4">
                    <p className="text-sm text-gray-600 mb-1"><strong>Original URL:</strong></p>
                    <p className="text-xs bg-gray-100 p-2 rounded break-all">{originalUrl}</p>
                  </div>
                  
                  <div className="mb-4">
                    <p className="text-sm text-gray-600 mb-1"><strong>Normalized URL:</strong></p>
                    <p className="text-xs bg-blue-100 p-2 rounded break-all">{normalizedUrl}</p>
                  </div>

                  {/* Video Test */}
                  <div className="mb-4">
                    <p className="text-sm text-gray-600 mb-2"><strong>Video Player Test:</strong></p>
                    <video
                      src={normalizedUrl}
                      className="w-full h-32 object-cover rounded border-2"
                      controls
                      preload="metadata"
                      onError={(e) => {
                        console.error('Video error for', normalizedUrl, e);
                        e.target.style.borderColor = 'red';
                        e.target.nextElementSibling.textContent = 'ERROR: Failed to load video';
                        e.target.nextElementSibling.className = 'text-red-600 text-sm mt-1';
                      }}
                      onLoadedMetadata={(e) => {
                        console.log('Video loaded successfully:', normalizedUrl);
                        e.target.style.borderColor = 'green';
                        e.target.nextElementSibling.textContent = 'SUCCESS: Video loaded';
                        e.target.nextElementSibling.className = 'text-green-600 text-sm mt-1';
                      }}
                      onLoadStart={(e) => {
                        e.target.nextElementSibling.textContent = 'LOADING: Video starting to load...';
                        e.target.nextElementSibling.className = 'text-blue-600 text-sm mt-1';
                      }}
                    />
                    <p className="text-gray-500 text-sm mt-1">Waiting for video...</p>
                  </div>

                  {/* Direct Link Test */}
                  <div className="space-y-2">
                    <a
                      href={normalizedUrl}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="block w-full text-center bg-blue-600 text-white py-2 px-4 rounded hover:bg-blue-700 transition-colors text-sm"
                    >
                      Open Direct Link
                    </a>
                    
                    <button
                      onClick={() => {
                        console.log('Testing HEAD request for:', normalizedUrl);
                        fetch(normalizedUrl, { method: 'HEAD' })
                          .then(response => {
                            console.log('HEAD request response:', response.status, response.headers);
                            const headers = {};
                            for (let [key, value] of response.headers.entries()) {
                              headers[key] = value;
                            }
                            alert(`HEAD request: ${response.status} ${response.statusText}\n\nHeaders:\n${JSON.stringify(headers, null, 2)}`);
                          })
                          .catch(error => {
                            console.error('HEAD request error:', error);
                            alert(`HEAD request failed: ${error.message}`);
                          });
                      }}
                      className="block w-full text-center bg-green-600 text-white py-2 px-4 rounded hover:bg-green-700 transition-colors text-sm"
                    >
                      Test HEAD Request
                    </button>

                    <button
                      onClick={() => {
                        console.log('Testing GET request for:', normalizedUrl);
                        fetch(normalizedUrl)
                          .then(response => {
                            console.log('GET request response:', response.status, response.headers);
                            if (response.ok) {
                              alert(`GET request successful: ${response.status} ${response.statusText}\nContent-Type: ${response.headers.get('content-type')}\nContent-Length: ${response.headers.get('content-length')}`);
                            } else {
                              alert(`GET request failed: ${response.status} ${response.statusText}`);
                            }
                          })
                          .catch(error => {
                            console.error('GET request error:', error);
                            alert(`GET request failed: ${error.message}`);
                          });
                      }}
                      className="block w-full text-center bg-purple-600 text-white py-2 px-4 rounded hover:bg-purple-700 transition-colors text-sm"
                    >
                      Test GET Request
                    </button>
                  </div>
                </div>
              </div>
            );
          })}
        </div>

        <div className="mt-8 bg-yellow-50 border border-yellow-200 rounded-lg p-4">
          <h3 className="text-lg font-semibold text-yellow-800 mb-2">Testing Instructions</h3>
          <ul className="text-yellow-700 space-y-1 text-sm">
            <li>• Videos should load with green borders when successful</li>
            <li>• Red borders indicate loading errors</li>
            <li>• Check browser console for detailed error messages</li>
            <li>• Test buttons will show request status in alerts</li>
            <li>• Direct links should open videos in new tabs</li>
          </ul>
        </div>
      </div>
    </div>
  );
}
