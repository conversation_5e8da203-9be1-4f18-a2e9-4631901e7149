'use client';

import { useEffect, useRef, useState } from 'react';
import { useRouter } from 'next/navigation';
import LoadingComponent from '@/components/LoadingComponent';

export default function SimpleHeroVideo({ videoPath }) {
  const videoRef = useRef(null);
  const router = useRouter();
  const [isLoading, setIsLoading] = useState(true);
  const [hasError, setHasError] = useState(false);
  const [isMobile, setIsMobile] = useState(false);

  // Detect mobile device
  useEffect(() => {
    const checkMobile = () => {
      const userAgent = navigator.userAgent || navigator.vendor || window.opera;
      const isMobileDevice = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(userAgent);
      setIsMobile(isMobileDevice);
    };

    checkMobile();
    // Also check on resize in case device orientation changes
    window.addEventListener('resize', checkMobile);
    return () => window.removeEventListener('resize', checkMobile);
  }, []);

  useEffect(() => {
    const video = videoRef.current;
    if (!video || !videoPath) {
      setIsLoading(false);
      setHasError(true);
      return;
    }

    // Simple event handlers
    const handleLoadStart = () => setIsLoading(true);
    const handleCanPlay = () => setIsLoading(false);
    const handleError = () => {
      setIsLoading(false);
      setHasError(true);
      // Redirect to 360s after error
      setTimeout(() => {
        router.push('/360s?id=entrance_360');
      }, 2000);
    };
    const handleEnded = () => {
      // Redirect to 360s when video ends
      router.push('/360s?id=entrance_360');
    };

    // Add event listeners
    video.addEventListener('loadstart', handleLoadStart);
    video.addEventListener('canplay', handleCanPlay);
    video.addEventListener('error', handleError);
    video.addEventListener('ended', handleEnded);

    // Try to play the video with mobile-specific handling
    const playVideo = async () => {
      try {
        video.muted = true; // Ensure muted for autoplay

        // Mobile-specific autoplay handling
        if (isMobile) {
          // On mobile, try multiple approaches for autoplay
          video.setAttribute('playsinline', 'true');
          video.setAttribute('webkit-playsinline', 'true');
          video.setAttribute('x5-playsinline', 'true'); // WeChat browser
          video.setAttribute('x5-video-player-type', 'h5'); // WeChat browser
          video.setAttribute('x5-video-player-fullscreen', 'false'); // WeChat browser

          // Force autoplay on mobile
          setTimeout(async () => {
            try {
              await video.play();
              setIsLoading(false);
            } catch (mobileError) {
              console.log('Mobile autoplay failed, trying again:', mobileError);
              // Try again after a short delay
              setTimeout(() => {
                video.play().catch(() => {
                  console.log('Final mobile autoplay attempt failed');
                  setIsLoading(false);
                });
              }, 500);
            }
          }, 100);
        } else {
          // Desktop autoplay
          await video.play();
          setIsLoading(false);
        }
      } catch (error) {
        console.log('Autoplay failed:', error);
        setIsLoading(false);
        // Don't treat autoplay failure as an error - user can manually play
      }
    };

    // Start playing when video can play
    video.addEventListener('canplay', playVideo, { once: true });

    // Mobile-specific: Try to start playing immediately
    if (isMobile) {
      // Force immediate play attempt on mobile
      const immediatePlay = () => {
        if (video.readyState >= 2) { // HAVE_CURRENT_DATA
          playVideo();
        }
      };

      // Try immediately and also on loadeddata
      immediatePlay();
      video.addEventListener('loadeddata', immediatePlay, { once: true });
    }

    // Cleanup
    return () => {
      video.removeEventListener('loadstart', handleLoadStart);
      video.removeEventListener('canplay', handleCanPlay);
      video.removeEventListener('error', handleError);
      video.removeEventListener('ended', handleEnded);
      video.removeEventListener('canplay', playVideo);
    };
  }, [videoPath, router, isMobile]);

  // Redirect if no video path
  useEffect(() => {
    if (!videoPath) {
      const timer = setTimeout(() => {
        router.push('/360s?id=entrance_360');
      }, 1000);
      return () => clearTimeout(timer);
    }
  }, [videoPath, router]);

  if (!videoPath) {
    return (
      // <div className="absolute inset-0 flex items-center justify-center bg-black">
      //   <div className="text-white text-center">
      //     <p className="text-lg mb-2">No video available</p>
      //     <p className="text-sm opacity-75">Redirecting to 360° view...</p>
      //   </div>
      // </div>
      <LoadingComponent/>
    );
  }

  if (hasError) {
    return (
      <div className="absolute inset-0 flex items-center justify-center bg-black">
        <div className="text-white text-center">
          <p className="text-lg mb-2">Video could not be loaded</p>
          <p className="text-sm opacity-75">Redirecting to 360° view...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="absolute inset-0 w-full h-full">
      {/* Mobile-specific styles to hide video controls */}
      {isMobile && (
        <style jsx>{`
          video::-webkit-media-controls {
            display: none !important;
          }
          video::-webkit-media-controls-panel {
            display: none !important;
          }
          video::-webkit-media-controls-play-button {
            display: none !important;
          }
          video::-webkit-media-controls-start-playback-button {
            display: none !important;
          }
          video::-webkit-media-controls-fullscreen-button {
            display: none !important;
          }
          video::-webkit-media-controls-timeline {
            display: none !important;
          }
          video::-webkit-media-controls-current-time-display {
            display: none !important;
          }
          video::-webkit-media-controls-time-remaining-display {
            display: none !important;
          }
          video::-webkit-media-controls-mute-button {
            display: none !important;
          }
          video::-webkit-media-controls-volume-slider {
            display: none !important;
          }
          video::-webkit-media-controls-overlay-play-button {
            display: none !important;
          }
          video::-webkit-media-controls-overlay-enclosure {
            display: none !important;
          }
        `}</style>
      )}
      {/* Loading overlay */}
      {isLoading && (
        <LoadingComponent/>
      )}

      {/* Video element with mobile-specific optimizations */}
      <video
        ref={videoRef}
        className="w-full h-full object-cover"
        src={videoPath}
        autoPlay
        muted
        playsInline
        preload="auto"
        controls={!isMobile} // Remove controls on mobile
        webkit-playsinline="true"
        x5-playsinline="true"
        x5-video-player-type="h5"
        x5-video-player-fullscreen="false"
        style={{
          width: '100%',
          height: '100%',
          objectFit: 'cover',
          // Hide any default video controls on mobile
          ...(isMobile && {
            WebkitAppearance: 'none',
            appearance: 'none',
          })
        }}
        // onTouchStart={isMobile ? (e) => {
        //   // Prevent default touch behavior that might show controls
        //   e.preventDefault();
        //   // Ensure video continues playing on touch
        //   if (videoRef.current && videoRef.current.paused) {
        //     videoRef.current.play().catch(console.log);
        //   }
        // } : undefined}
        onContextMenu={isMobile ? (e) => e.preventDefault() : undefined} // Disable right-click menu on mobile
      >
        <source src={videoPath} type="video/mp4" />
        Your browser does not support the video tag.
      </video>

      {/* Mobile-specific overlay to prevent interaction with video controls */}
      {isMobile && (
        <div
          className="absolute inset-0 w-full h-full z-10 pointer-events-none"
          style={{
            background: 'transparent',
            touchAction: 'none'
          }}
        />
      )}
    </div>
  );
}
