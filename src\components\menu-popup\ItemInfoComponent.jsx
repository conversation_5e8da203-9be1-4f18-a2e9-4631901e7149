'use client'
import ImageWrapperResponsive from '../ImageWrapperResponsive'
import { useEffect, useState } from 'react'
import { useContextExperience } from '@/contexts/useContextExperience'
import SpinerComponent from '../SpinerComponent'

export default function ItemInfoComponent() {
  const [error,setError]=useState('')
  const [showError,setShowError]=useState(false)
  const [loading,setLoading]=useState(false)
  const [data,setData]=useState(null)
  const {experienceState,disptachExperience}=useContextExperience()

  const fetchData = async (id) => {
    try {
      setLoading(true)
      const serverResponse=await fetch(`/api/info-markers/${id}`)
      const responseData=await serverResponse.json()
      // console.log(responseData)
      if(!data){
        setError('Failed to load data')
        setShowError(true)
      }
      // console.log(responseData?.data)
      setData(responseData?.data)
      setLoading(false)
      // return responseData
    } catch (error) {
      console.log(error)
      setError(error.message)
      setShowError(true)
    }
  }

  useEffect(() => {
    fetchData(experienceState?.showItemInfo?.id)
  }, [experienceState?.showItemInfo?.id])

  console.log('ItemInfoComponent:',data)
  
  return (
    <div className='flex w-full h-fit text-white'>
      {/* {showError
        ? <div className='flex w-full h-full items-center justify-center'>{error}</div> 
        : <div className='flex w-full h-full items-start justify-start'>
            {loading 
              ? <div className='text-center'><span className='text-sm'>loading...</span></div>  
              : <div className='flex w-full h-full items-start justify-start'>
                  <ImageWrapperResponsive src={data?.image}/>
                </div>
            }
          </div>
      } */}
      {loading 
        ? <div className='flex w-full h-full items-center justify-center'><SpinerComponent/></div>  
        : <div className='flex w-full h-full flex-col items-start justify-start mt-16'>
            {/* <ImageWrapperResponsive src={data?.image} className='w-full h-full'/> */}
            <div className='flex flex-col mb-20 w-full h-fit items-center justify-start gap-5 overflow-y-auto'>
              <div className='flex flex-col w-full h-fit items-center justify-start gap-10'>
                <div className='w-full h-96 relative'>
                  <ImageWrapperResponsive src={data?.image} alt='page image' className='object-cover'/>
                </div>
                <div className='flex w-full h-fit gap-10'>
                  <h1 className='w-1/4 text-6xl text-left leading-12 uppercase'>
                    {data?.title}
                  </h1>
                  <div className='flex flex-col w-full gap-5'>
                    <p className='text-left leading-7 uppercase text-3xl'>
                      {data?.body1}
                    </p>
                    <p className='text-left wfu leading-7'>
                      {data?.body2}
                    </p>
                  </div>
                </div>
                {data?.secondaryEntries?.map((i,index)=>(
                  <div key={index} className='flex w-full h-fit items-center justify-start gap-10'>
                    <div className='w-1/3 h-60 relative'>
                      <ImageWrapperResponsive src={i?.image} alt='page image' className='object-cover'/>
                    </div>
                    <div className='flex w-2/3 flex-col h-fit gap-5'>
                      <h1 className='w-full text-6xl text-left leading-12 uppercase'>
                        {i?.title}
                      </h1>
                      <p className='w-full text-left leading-7 text-3xl'>
                        {i?.body1}
                      </p>
                      <p className='w-full text-left leading-7'>
                        {i?.body2}
                      </p>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>
      }
    </div>
  )
}
