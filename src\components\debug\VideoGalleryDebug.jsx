'use client'
import React, { useState, useEffect } from 'react'

export default function VideoGalleryDebug() {
  const [apiData, setApiData] = useState(null)
  const [testData, setTestData] = useState(null)
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState('')

  const testMainAPI = async () => {
    setLoading(true)
    setError('')
    try {
      const response = await fetch('/api/video-gallery')
      const data = await response.json()
      setApiData(data)
      console.log('Main API Response:', data)
    } catch (err) {
      setError(`Main API Error: ${err.message}`)
      console.error('Main API Error:', err)
    } finally {
      setLoading(false)
    }
  }

  const testDatabaseAPI = async () => {
    setLoading(true)
    setError('')
    try {
      const response = await fetch('/api/video-gallery/test')
      const data = await response.json()
      setTestData(data)
      console.log('Test API Response:', data)
    } catch (err) {
      setError(`Test API Error: ${err.message}`)
      console.error('Test API Error:', err)
    } finally {
      setLoading(false)
    }
  }

  const createSampleVideo = async () => {
    setLoading(true)
    setError('')
    try {
      const response = await fetch('/api/video-gallery/test', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          title: 'Debug Test Video',
          url: 'https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/BigBuckBunny.mp4'
        })
      })
      const data = await response.json()
      console.log('Create Sample Response:', data)
      // Refresh the main API data
      await testMainAPI()
    } catch (err) {
      setError(`Create Sample Error: ${err.message}`)
      console.error('Create Sample Error:', err)
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    testMainAPI()
    testDatabaseAPI()
  }, [])

  return (
    <div className="p-6 bg-gray-100 min-h-screen">
      <div className="max-w-4xl mx-auto">
        <h1 className="text-2xl font-bold mb-6 text-gray-800">Video Gallery Debug Panel</h1>
        
        {error && (
          <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
            {error}
          </div>
        )}

        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {/* Main API Test */}
          <div className="bg-white p-6 rounded-lg shadow">
            <h2 className="text-lg font-semibold mb-4 text-gray-800">Main API Test</h2>
            <button
              onClick={testMainAPI}
              disabled={loading}
              className="bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600 disabled:opacity-50 mb-4"
            >
              {loading ? 'Testing...' : 'Test /api/video-gallery'}
            </button>
            
            {apiData && (
              <div className="mt-4">
                <h3 className="font-medium text-gray-700 mb-2">Response:</h3>
                <pre className="bg-gray-100 p-3 rounded text-xs overflow-auto max-h-64">
                  {JSON.stringify(apiData, null, 2)}
                </pre>
                {apiData.success && apiData.data && (
                  <div className="mt-2 text-sm text-green-600">
                    ✅ Found {apiData.data.length} video(s)
                  </div>
                )}
              </div>
            )}
          </div>

          {/* Database Test */}
          <div className="bg-white p-6 rounded-lg shadow">
            <h2 className="text-lg font-semibold mb-4 text-gray-800">Database Test</h2>
            <button
              onClick={testDatabaseAPI}
              disabled={loading}
              className="bg-green-500 text-white px-4 py-2 rounded hover:bg-green-600 disabled:opacity-50 mb-4"
            >
              {loading ? 'Testing...' : 'Test Database Connection'}
            </button>
            
            {testData && (
              <div className="mt-4">
                <h3 className="font-medium text-gray-700 mb-2">Response:</h3>
                <pre className="bg-gray-100 p-3 rounded text-xs overflow-auto max-h-64">
                  {JSON.stringify(testData, null, 2)}
                </pre>
                {testData.success && (
                  <div className="mt-2 text-sm text-green-600">
                    ✅ Database connection successful
                  </div>
                )}
              </div>
            )}
          </div>

          {/* Create Sample Data */}
          <div className="bg-white p-6 rounded-lg shadow md:col-span-2">
            <h2 className="text-lg font-semibold mb-4 text-gray-800">Create Sample Data</h2>
            <button
              onClick={createSampleVideo}
              disabled={loading}
              className="bg-purple-500 text-white px-4 py-2 rounded hover:bg-purple-600 disabled:opacity-50 mb-4"
            >
              {loading ? 'Creating...' : 'Create Sample Video'}
            </button>
            <p className="text-sm text-gray-600">
              This will create a sample video entry with a publicly available test video URL.
            </p>
          </div>

          {/* Video URLs Test */}
          {apiData?.data && apiData.data.length > 0 && (
            <div className="bg-white p-6 rounded-lg shadow md:col-span-2">
              <h2 className="text-lg font-semibold mb-4 text-gray-800">Video URLs Test</h2>
              <div className="space-y-4">
                {apiData.data.map((video, index) => (
                  <div key={video._id || index} className="border p-4 rounded">
                    <h3 className="font-medium text-gray-700 mb-2">{video.title}</h3>
                    <p className="text-sm text-gray-600 mb-2">URL: {video.url}</p>
                    <video
                      src={video.url}
                      className="w-full max-w-md h-32 object-cover rounded"
                      controls
                      preload="metadata"
                      onError={(e) => {
                        console.error('Video load error:', e)
                        e.target.style.border = '2px solid red'
                      }}
                      onLoadedMetadata={() => {
                        console.log('Video loaded successfully:', video.url)
                      }}
                    />
                  </div>
                ))}
              </div>
            </div>
          )}
        </div>

        {/* Instructions */}
        <div className="bg-yellow-50 border border-yellow-200 p-4 rounded mt-6">
          <h3 className="font-medium text-yellow-800 mb-2">Debug Instructions:</h3>
          <ol className="list-decimal list-inside text-sm text-yellow-700 space-y-1">
            <li>First, test the main API to see if it returns any data</li>
            <li>Test the database connection to ensure MongoDB is working</li>
            <li>If no videos exist, create sample data using the button above</li>
            <li>Check the browser console for detailed error messages</li>
            <li>Test individual video URLs to see if they load properly</li>
          </ol>
        </div>
      </div>
    </div>
  )
}
