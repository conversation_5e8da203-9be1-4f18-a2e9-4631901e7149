import { useState, useRef, useEffect, useCallback } from 'react'
import { normalizeVideoUrl } from '@/lib/video-url-utils'

/**
 * Custom hook for video player functionality with iPad/iOS optimization
 */
export const useVideoPlayer = (videoUrl) => {
  const videoRef = useRef(null)
  const normalizedUrl = normalizeVideoUrl(videoUrl)
  const [isPlaying, setIsPlaying] = useState(false)
  const [currentTime, setCurrentTime] = useState(0)
  const [duration, setDuration] = useState(0)
  const [volume, setVolume] = useState(1)
  const [isMuted, setIsMuted] = useState(false)
  const [isLoading, setIsLoading] = useState(true)
  const [hasError, setHasError] = useState(false)
  const [isBuffering, setIsBuffering] = useState(false)
  const [canPlay, setCanPlay] = useState(false)
  const [isMobile, setIsMobile] = useState(false)

  // Detect mobile device
  useEffect(() => {
    const checkMobile = () => {
      const userAgent = navigator.userAgent || navigator.vendor || window.opera
      const isMobileDevice = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(userAgent)
      setIsMobile(isMobileDevice)
    }
    checkMobile()
  }, [])

  // Reset states when video URL changes
  useEffect(() => {
    if (normalizedUrl) {
      setIsLoading(true)
      setHasError(false)
      setCanPlay(false)
      setCurrentTime(0)
      setIsPlaying(false)
    }
  }, [normalizedUrl])

  // Play/pause with iOS compatibility
  const togglePlayPause = useCallback(async () => {
    if (!videoRef.current) return

    try {
      if (isPlaying) {
        videoRef.current.pause()
      } else {
        // For iOS/iPad, handle play() promise
        const playPromise = videoRef.current.play()
        if (playPromise !== undefined) {
          await playPromise
        }
      }
    } catch (error) {
      console.warn('Video play failed:', error)
      setHasError(true)
    }
  }, [isPlaying])

  // Seek to specific time
  const seekTo = useCallback((time) => {
    if (videoRef.current && duration > 0) {
      const clampedTime = Math.max(0, Math.min(duration, time))
      videoRef.current.currentTime = clampedTime
      setCurrentTime(clampedTime)
    }
  }, [duration])

  // Skip time (forward/backward)
  const skipTime = useCallback((seconds) => {
    if (videoRef.current) {
      const newTime = Math.max(0, Math.min(duration, currentTime + seconds))
      videoRef.current.currentTime = newTime
      setCurrentTime(newTime)
    }
  }, [currentTime, duration])

  // Volume control
  const setVideoVolume = useCallback((newVolume) => {
    const clampedVolume = Math.max(0, Math.min(1, newVolume))
    setVolume(clampedVolume)
    if (videoRef.current) {
      videoRef.current.volume = clampedVolume
      setIsMuted(clampedVolume === 0)
    }
  }, [])

  // Toggle mute
  const toggleMute = useCallback(() => {
    if (!videoRef.current) return

    if (isMuted) {
      videoRef.current.volume = volume
      setIsMuted(false)
    } else {
      videoRef.current.volume = 0
      setIsMuted(true)
    }
  }, [isMuted, volume])

  // Format time helper
  const formatTime = useCallback((time) => {
    if (isNaN(time)) return '0:00'
    const minutes = Math.floor(time / 60)
    const seconds = Math.floor(time % 60)
    return `${minutes}:${seconds.toString().padStart(2, '0')}`
  }, [])

  // Video event handlers with enhanced debugging
  const videoEventHandlers = {
    onLoadStart: () => {
      console.log('Video load started for:', videoUrl)
      setIsLoading(true)
      setHasError(false)
    },
    onLoadedMetadata: () => {
      console.log('Video metadata loaded for:', videoUrl)
      if (videoRef.current) {
        setDuration(videoRef.current.duration)
        setIsLoading(false)
        videoRef.current.volume = volume
        setCanPlay(true)
      }
    },
    onTimeUpdate: () => {
      if (videoRef.current) {
        setCurrentTime(videoRef.current.currentTime)
      }
    },
    onPlay: () => {
      console.log('Video play started for:', videoUrl)
      setIsPlaying(true)
      setIsBuffering(false)
    },
    onPause: () => {
      console.log('Video paused for:', videoUrl)
      setIsPlaying(false)
    },
    onWaiting: () => {
      console.log('Video waiting/buffering for:', videoUrl)
      setIsBuffering(true)
    },
    onCanPlay: () => {
      console.log('Video can play for:', videoUrl)
      setIsBuffering(false)
      setCanPlay(true)
    },
    onCanPlayThrough: () => {
      console.log('Video can play through for:', videoUrl)
      setIsBuffering(false)
      setCanPlay(true)
    },
    onError: (e) => {
      console.error('Video error for:', videoUrl, e)
      console.error('Video error details:', {
        error: e.target.error,
        networkState: e.target.networkState,
        readyState: e.target.readyState,
        src: e.target.src,
        currentSrc: e.target.currentSrc
      })
      setHasError(true)
      setIsLoading(false)
      setIsBuffering(false)
    },
    onEnded: () => {
      console.log('Video ended for:', videoUrl)
      setIsPlaying(false)
      setCurrentTime(0)
    },
    onStalled: () => {
      console.log('Video stalled for:', videoUrl)
      setIsBuffering(true)
    },
    onSuspend: () => {
      console.log('Video suspended for:', videoUrl)
      setIsBuffering(false)
    },
  }

  return {
    // Refs
    videoRef,
    
    // States
    isPlaying,
    currentTime,
    duration,
    volume,
    isMuted,
    isLoading,
    hasError,
    isBuffering,
    canPlay,
    isMobile,
    
    // Actions
    togglePlayPause,
    seekTo,
    skipTime,
    setVideoVolume,
    toggleMute,
    formatTime,
    
    // Event handlers
    videoEventHandlers,
  }
}

export default useVideoPlayer
