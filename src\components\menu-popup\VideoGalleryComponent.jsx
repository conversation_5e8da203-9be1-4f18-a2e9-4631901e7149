'use client'
import React from 'react'
import { useContextExperience } from '@/contexts/useContextExperience'
import { useEffect, useState } from 'react'
import ImageWrapperResponsive from '../ImageWrapperResponsive'
import SpinerComponent from '../SpinerComponent'
import VideoPlayer from './VideoPlayer'

function RoverOverButton({data,handleVideoClick}) {
  const [onhover,setOnHover]=useState(false)
  return(
    <div 
      onClick={()=>handleVideoClick(data)}
      onMouseEnter={()=>setOnHover(true)} 
      onMouseLeave={()=>setOnHover(false)} 
      className='z-10 absolute w-fit h-fit m-auto'
    >
        {onhover ? <ImageWrapperResponsive src={'assets/video_btn_ov.png'}/> :
        <ImageWrapperResponsive src={'assets/video_btn_off.png'}/>}
    </div>
  )
}

export default function VideoGalleryComponent() {
  const [error,setError]=useState('')
  const [showError,setShowError]=useState(false)
  const [loading,setLoading]=useState(false)
  const [data,setData]=useState(null)
  const [showVideoPlayer,setShowVideoPlayer]=useState(false) // show video player state
  const [videoData,setVideoData]=useState({}) // show video player state
  const {experienceState,disptachExperience}=useContextExperience()

  const fetchData = async (id) => {
    try {
      setLoading(true)
      const serverResponse=await fetch(`/api/video-gallery`)
      const responseData=await serverResponse.json()
      if(!data){
        setError('Failed to load data')
        setShowError(true)
      }
      console.log(responseData?.data)
      setData(responseData?.data)
      setLoading(false)
      // return responseData
    } catch (error) {
      console.log(error)
      setError(error.message)
      setShowError(true)
    }
  }

  useEffect(() => {
    fetchData()
  }, [])

  const handleVideoClick = (item) => { 
    setShowVideoPlayer(true)
    setVideoData(item)
  }
  
  console.log('VideoGalleryComponent:',videoData)
  
  return (
    <div className='flex mt-16 w-full h-fit text-white'>
      {loading 
        ? <SpinerComponent/>  
        : <div className='flex w-full h-full items-start justify-start'>
            <div className='grid md:grid-cols-2 grid-cols-1 gap-4'>
              {data?.map((item) => (
                <div key={item._id} className='flex relative w-full h-80 flex-col items-center justify-center'>
                  <video
                    src={item.url}
                    className="w-full h-full object-cover rounded-md"
                    muted
                    preload="metadata"
                    playsInline
                    webkit-playsinline="true"
                    poster={item.thumbnail}
                    onError={(e) => {
                      console.warn('Video preview error:', e)
                      // Fallback to a placeholder or hide the video
                    }}
                  />
                  <RoverOverButton data={item} handleVideoClick={handleVideoClick}/>
                </div>
              ))} 
            </div>
          </div>
      }
      {showVideoPlayer && videoData && <VideoPlayer data={videoData} setShowVideoPlayer={setShowVideoPlayer}/>}
    </div>
  )
}
