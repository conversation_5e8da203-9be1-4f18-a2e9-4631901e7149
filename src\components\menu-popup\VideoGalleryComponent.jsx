'use client'
import React, { useEffect, useState } from 'react'
import ImageWrapperResponsive from '../ImageWrapperResponsive'
import SpinerComponent from '../SpinerComponent'
import OptimizedVideoPlayer from './OptimizedVideoPlayer'

function RoverOverButton({data, handleVideoClick}) {
  const [onhover, setOnHover] = useState(false)

  const handleClick = (e) => {
    e.preventDefault()
    e.stopPropagation()
    if (data && data.url) {
      handleVideoClick(data)
    } else {
      console.warn('Invalid video data:', data)
    }
  }

  return(
    <div
      onClick={handleClick}
      onMouseEnter={() => setOnHover(true)}
      onMouseLeave={() => setOnHover(false)}
      onTouchStart={() => setOnHover(true)}
      onTouchEnd={() => setOnHover(false)}
      className='z-10 absolute w-fit h-fit m-auto cursor-pointer transition-transform duration-200 hover:scale-110 active:scale-95'
      style={{ WebkitTapHighlightColor: 'transparent' }}
      role="button"
      aria-label={`Play video: ${data?.title || 'Untitled'}`}
    >
      {onhover ?
        <ImageWrapperResponsive src={'assets/video_btn_ov.png'} alt="Play video (hover)"/> :
        <ImageWrapperResponsive src={'assets/video_btn_off.png'} alt="Play video"/>
      }
    </div>
  )
}

export default function VideoGalleryComponent() {
  const [error, setError] = useState('')
  const [showError, setShowError] = useState(false)
  const [loading, setLoading] = useState(false)
  const [data, setData] = useState(null)
  const [showVideoPlayer, setShowVideoPlayer] = useState(false)
  const [videoData, setVideoData] = useState(null)
  const [retryCount, setRetryCount] = useState(0)

  const fetchData = async () => {
    try {
      setLoading(true)
      setError('')
      setShowError(false)

      console.log(`Fetching video gallery data (attempt ${retryCount + 1})...`)

      const serverResponse = await fetch(`/api/video-gallery`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
        cache: 'no-cache'
      })

      if (!serverResponse.ok) {
        throw new Error(`HTTP ${serverResponse.status}: ${serverResponse.statusText}`)
      }

      const responseData = await serverResponse.json()

      if (!responseData.success) {
        throw new Error(responseData.message || 'Failed to fetch video gallery data')
      }

      console.log('Video gallery data loaded successfully:', responseData?.data)
      setData(responseData?.data || [])
      setLoading(false)
      setRetryCount(0) // Reset retry count on success
    } catch (error) {
      console.error('Error fetching video gallery:', error)
      setError(error.message)
      setShowError(true)
      setLoading(false)
      setData([])
      setRetryCount(prev => prev + 1)
    }
  }

  useEffect(() => {
    fetchData()
  }, [])

  // Auto-retry mechanism for failed requests
  useEffect(() => {
    if (showError && retryCount < 3) {
      const retryTimeout = setTimeout(() => {
        console.log(`Auto-retrying video gallery fetch (attempt ${retryCount + 1})...`)
        fetchData()
      }, 2000 * retryCount) // Exponential backoff: 2s, 4s, 6s

      return () => clearTimeout(retryTimeout)
    }
  }, [showError, retryCount])

  const handleVideoClick = (item) => {
    console.log('Video clicked:', item)
    if (item && item.url) {
      setVideoData(item)
      setShowVideoPlayer(true)
    } else {
      console.error('Invalid video item:', item)
      setError('Invalid video data')
      setShowError(true)
    }
  }

  // Close video player handler
  const handleCloseVideoPlayer = () => {
    setShowVideoPlayer(false)
    setVideoData(null)
  }
  
  return (
    <div className='flex mt-16 w-full h-fit text-white'>
      {loading ? (
        <SpinerComponent/>
      ) : showError ? (
        <div className='flex w-full h-full items-center justify-center min-h-[400px]'>
          <div className='text-center text-red-400 max-w-md mx-auto p-6'>
            <div className='mb-4'>
              <svg className="mx-auto h-16 w-16 text-red-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
            </div>
            <h3 className='text-lg font-medium mb-2'>Error Loading Videos</h3>
            <p className='text-sm mb-4 text-gray-300'>{error}</p>
            {retryCount > 0 && (
              <p className='text-xs mb-4 text-gray-400'>Retry attempt: {retryCount}</p>
            )}
            <div className='space-y-2'>
              <button
                onClick={fetchData}
                disabled={loading}
                className='px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed'
              >
                {loading ? 'Retrying...' : 'Try Again'}
              </button>
              <div className='text-xs text-gray-400'>
                <p>If this persists, check the debug panel at:</p>
                <a
                  href="/debug/video-gallery"
                  target="_blank"
                  rel="noopener noreferrer"
                  className='text-blue-400 hover:text-blue-300 underline'
                >
                  /debug/video-gallery
                </a>
              </div>
            </div>
          </div>
        </div>
      ) : !data || data.length === 0 ? (
        <div className='flex w-full h-full items-center justify-center min-h-[400px]'>
          <div className='text-center text-gray-400 max-w-md mx-auto p-6'>
            <div className='mb-4'>
              <svg className="mx-auto h-16 w-16 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z" />
              </svg>
            </div>
            <h3 className='text-lg font-medium mb-2'>No Videos Available</h3>
            <p className='text-sm mb-4'>No video gallery items found.</p>
            <div className='text-xs text-gray-500'>
              <p>Videos can be added through the admin panel at:</p>
              <a
                href="/admin/videos"
                target="_blank"
                rel="noopener noreferrer"
                className='text-blue-400 hover:text-blue-300 underline'
              >
                /admin/videos
              </a>
            </div>
          </div>
        </div>
      ) : (
        <div className='flex w-full h-full items-start justify-start'>
          <div className='grid md:grid-cols-2 grid-cols-1 gap-6 w-full'>
            {data.map((item, index) => (
              <div
                key={item._id || index}
                className='flex relative w-full h-80 flex-col items-center justify-center bg-gray-900 rounded-lg overflow-hidden shadow-lg hover:shadow-xl transition-shadow duration-300'
              >
                <video
                  src={item.url}
                  className="w-full h-full object-cover"
                  muted
                  preload="metadata"
                  playsInline
                  webkit-playsinline="true"
                  poster={item.thumbnail}
                  onError={(e) => {
                    console.warn('Video preview error for:', item.url, e)
                    // Add visual indicator for failed videos
                    e.target.style.display = 'none'
                    const parent = e.target.parentElement
                    if (parent && !parent.querySelector('.error-placeholder')) {
                      const errorDiv = document.createElement('div')
                      errorDiv.className = 'error-placeholder absolute inset-0 flex items-center justify-center bg-gray-800 text-gray-400'
                      errorDiv.innerHTML = `
                        <div class="text-center">
                          <svg class="mx-auto h-12 w-12 mb-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                          </svg>
                          <p class="text-sm">Video preview failed</p>
                        </div>
                      `
                      parent.appendChild(errorDiv)
                    }
                  }}
                  onLoadStart={() => {
                    console.log('Loading video preview:', item.url)
                  }}
                  onLoadedMetadata={() => {
                    console.log('Video preview loaded:', item.url)
                  }}
                />

                {/* Video Title Overlay */}
                {item.title && (
                  <div className='absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black/80 to-transparent p-3'>
                    <h4 className='text-white text-sm font-medium truncate'>{item.title}</h4>
                  </div>
                )}

                <RoverOverButton data={item} handleVideoClick={handleVideoClick}/>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Video Player Modal */}
      {showVideoPlayer && videoData && (
        <OptimizedVideoPlayer
          data={videoData}
          setShowVideoPlayer={handleCloseVideoPlayer}
        />
      )}
    </div>
  )
}
