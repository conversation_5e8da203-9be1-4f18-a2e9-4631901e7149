'use client'
import React from 'react'
import { useContextExperience } from '@/contexts/useContextExperience'
import { useEffect, useState } from 'react'
import ImageWrapperResponsive from '../ImageWrapperResponsive'
import SpinerComponent from '../SpinerComponent'
import OptimizedVideoPlayer from './OptimizedVideoPlayer'

function RoverOverButton({data,handleVideoClick}) {
  const [onhover,setOnHover]=useState(false)
  return(
    <div 
      onClick={()=>handleVideoClick(data)}
      onMouseEnter={()=>setOnHover(true)} 
      onMouseLeave={()=>setOnHover(false)} 
      className='z-10 absolute w-fit h-fit m-auto'
    >
        {onhover ? <ImageWrapperResponsive src={'assets/video_btn_ov.png'}/> :
        <ImageWrapperResponsive src={'assets/video_btn_off.png'}/>}
    </div>
  )
}

export default function VideoGalleryComponent() {
  const [error,setError]=useState('')
  const [showError,setShowError]=useState(false)
  const [loading,setLoading]=useState(false)
  const [data,setData]=useState(null)
  const [showVideoPlayer,setShowVideoPlayer]=useState(false) // show video player state
  const [videoData,setVideoData]=useState({}) // show video player state
  const {experienceState,disptachExperience}=useContextExperience()

  const fetchData = async () => {
    try {
      setLoading(true)
      setError('')
      setShowError(false)

      const serverResponse = await fetch(`/api/video-gallery`)

      if (!serverResponse.ok) {
        throw new Error(`HTTP error! status: ${serverResponse.status}`)
      }

      const responseData = await serverResponse.json()

      if (!responseData.success) {
        throw new Error(responseData.message || 'Failed to fetch video gallery data')
      }

      console.log('Video gallery data:', responseData?.data)
      setData(responseData?.data || [])
      setLoading(false)
    } catch (error) {
      console.error('Error fetching video gallery:', error)
      setError(error.message)
      setShowError(true)
      setLoading(false)
      setData([])
    }
  }

  useEffect(() => {
    fetchData()
  }, [])

  const handleVideoClick = (item) => { 
    setShowVideoPlayer(true)
    setVideoData(item)
  }
  
  console.log('VideoGalleryComponent:',videoData)
  
  return (
    <div className='flex mt-16 w-full h-fit text-white'>
      {loading ? (
        <SpinerComponent/>
      ) : showError ? (
        <div className='flex w-full h-full items-center justify-center'>
          <div className='text-center text-red-400'>
            <h3 className='text-lg font-medium mb-2'>Error Loading Videos</h3>
            <p className='text-sm mb-4'>{error}</p>
            <button
              onClick={fetchData}
              className='px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 transition-colors'
            >
              Try Again
            </button>
          </div>
        </div>
      ) : !data || data.length === 0 ? (
        <div className='flex w-full h-full items-center justify-center'>
          <div className='text-center text-gray-400'>
            <h3 className='text-lg font-medium mb-2'>No Videos Available</h3>
            <p className='text-sm'>No video gallery items found.</p>
          </div>
        </div>
      ) : (
        <div className='flex w-full h-full items-start justify-start'>
          <div className='grid md:grid-cols-2 grid-cols-1 gap-4'>
            {data.map((item) => (
              <div key={item._id} className='flex relative w-full h-80 flex-col items-center justify-center'>
                <video
                  src={item.url}
                  className="w-full h-full object-cover rounded-md"
                  muted
                  preload="metadata"
                  playsInline
                  webkit-playsinline="true"
                  poster={item.thumbnail}
                  onError={(e) => {
                    console.warn('Video preview error for:', item.url, e)
                  }}
                  onLoadStart={() => {
                    console.log('Loading video preview:', item.url)
                  }}
                />
                <RoverOverButton data={item} handleVideoClick={handleVideoClick}/>
              </div>
            ))}
          </div>
        </div>
      )}
      {showVideoPlayer && videoData && <OptimizedVideoPlayer data={videoData} setShowVideoPlayer={setShowVideoPlayer}/>}
    </div>
  )
}
