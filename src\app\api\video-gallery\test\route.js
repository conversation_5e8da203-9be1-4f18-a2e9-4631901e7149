import { NextResponse } from 'next/server';
import connectDB from '@/lib/mongodb';
import { VideoGallery } from '@/models/VideoGallery';

// GET /api/video-gallery/test - Test endpoint to check database connection and create sample data
export async function GET(request) {
  try {
    await connectDB();
    
    // Check if any video gallery items exist
    const count = await VideoGallery.countDocuments();
    
    // If no items exist, create some sample data
    if (count === 0) {
      const sampleVideos = [
        {
          title: 'Sample Video 1',
          url: 'https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/BigBuckBunny.mp4'
        },
        {
          title: 'Sample Video 2', 
          url: 'https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/ElephantsDream.mp4'
        },
        {
          title: 'Sample Video 3',
          url: 'https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/ForBiggerBlazes.mp4'
        }
      ];
      
      await VideoGallery.insertMany(sampleVideos);
      
      return NextResponse.json({
        success: true,
        message: 'Sample video gallery items created successfully',
        data: {
          created: sampleVideos.length,
          total: sampleVideos.length
        }
      });
    }
    
    // If items exist, just return the count
    const items = await VideoGallery.find().limit(5).lean();
    
    return NextResponse.json({
      success: true,
      message: 'Video gallery database connection successful',
      data: {
        total: count,
        sample: items
      }
    });
    
  } catch (error) {
    console.error('Video gallery test error:', error);
    return NextResponse.json(
      {
        success: false,
        error: 'Database connection failed',
        message: error.message,
        details: {
          name: error.name,
          stack: process.env.NODE_ENV === 'development' ? error.stack : undefined
        }
      },
      { status: 500 }
    );
  }
}

// POST /api/video-gallery/test - Create a single test video
export async function POST(request) {
  try {
    await connectDB();
    
    const body = await request.json();
    const { title = 'Test Video', url = 'https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/BigBuckBunny.mp4' } = body;
    
    const testVideo = new VideoGallery({
      title,
      url
    });
    
    await testVideo.save();
    
    return NextResponse.json({
      success: true,
      message: 'Test video created successfully',
      data: testVideo
    });
    
  } catch (error) {
    console.error('Test video creation error:', error);
    return NextResponse.json(
      {
        success: false,
        error: 'Failed to create test video',
        message: error.message
      },
      { status: 500 }
    );
  }
}
